package main

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/mysqldialect"
)

var (
	db    *bun.DB
	sqlDb *sql.DB
	err   error
)

func init() {
	sqlDb, err = sql.Open("mysql", "root_ts:gx_iuOi6uVwgt78ioPlM_we66BnJ@tcp(127.0.0.1:3307)/paipan?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		panic(err)
	}
	if err = sqlDb.Ping(); err != nil {
		panic(err)
	}
	sqlDb.SetMaxIdleConns(10)
	sqlDb.SetMaxOpenConns(100)
	sqlDb.SetConnMaxLifetime(time.Hour)
	db = bun.NewDB(sqlDb, mysqldialect.New())
}

func main() {
	err := AnalysisOneDay(context.TODO(), "2025-08-25")
	if err != nil {
		panic(err)
	}
}

func AnalysisOneDay(ctx context.Context, date string) error {
	var (
		fromAppCollect       FromAppCollect
		fromAppUser          FromAppUser
		fromUserPaipanRecord FromUserPaipanRecord
		fromCalcUsers        FromCalcUsers
		fromUserHepanRecord  FromUserHepanRecord
		fromUserOrder        FromUserOrder
		fromQwContactFollow  FromQwContactFollow
		q                    string
		err                  error
	)

	q = setQuery(QueryFromAppCollect, date)
	err = db.NewRaw(q).Scan(ctx, &fromAppCollect)
	if err != nil {
		return err
	}
	q = setQuery(QueryFromAppUser, date)
	err = db.NewRaw(q).Scan(ctx, &fromAppUser)
	if err != nil {
		return err
	}
	q = setQuery(QueryFromUserPaipanRecord, date)
	err = db.NewRaw(q).Scan(ctx, &fromUserPaipanRecord)
	if err != nil {
		return err
	}
	q = setQuery(QueryFromCalcUsers, date)
	err = db.NewRaw(q).Scan(ctx, &fromCalcUsers)
	if err != nil {
		return err
	}
	q = setQuery(QueryFromUserHepanRecord, date)
	err = db.NewRaw(q).Scan(ctx, &fromUserHepanRecord)
	if err != nil {
		return err
	}
	q = setQuery(QueryFromUserOrder, date)
	err = db.NewRaw(q).Scan(ctx, &fromUserOrder)
	if err != nil {
		return err
	}
	q = setQuery(QueryFromQwContactFollow, date)
	err = db.NewRaw(q).Scan(ctx, &fromQwContactFollow)
	if err != nil {
		return err
	}

	calcCount := fromUserPaipanRecord.calcPaipanCount + fromUserPaipanRecord.calcYunshiCount + fromUserPaipanRecord.calcLuncaiCount + fromUserPaipanRecord.calcHepanCount + fromUserPaipanRecord.calcPeishiCount + fromUserPaipanRecord.calcGaokaoCount + fromUserPaipanRecord.calcQingganCount + fromUserHepanRecord.CalcHepanCount
	analysis := &DailyUserAnalysis{
		Date:                          date,
		NewActivatedUsers:             fromAppCollect.newActivatedUsers,
		NewRegisteredUsers:            fromAppUser.newRegisteredUsers,
		NewRegisteredUsersWithDeleted: fromAppUser.newRegisteredUsersWithDeleted,
		NewQwContactUsers:             fromQwContactFollow.NewQwContactUsers,
		ActiveUsers:                   fromAppCollect.activeUsers,
		TotalUsers:                    fromAppUser.totalUsers,
		TotalUsersWithDeleted:         fromAppUser.totalUsersWithDeleted,
		CalcUsers:                     fromCalcUsers.CalcUsers,
		CalcCount:                     calcCount,
		CalcAvgCount:                  float64(calcCount) / float64(fromCalcUsers.CalcUsers),
		CalcPaipanUsers:               fromUserPaipanRecord.calcPaipanUsers,
		CalcPaipanCount:               fromUserPaipanRecord.calcPaipanCount,
		CalcYunshiUsers:               fromUserPaipanRecord.calcYunshiUsers,
		CalcYunshiCount:               fromUserPaipanRecord.calcYunshiCount,
		PageYunshiUsers:               fromAppCollect.pageYunshiUsers,
		CalcLuncaiUsers:               fromUserPaipanRecord.calcLuncaiUsers,
		CalcLuncaiCount:               fromUserPaipanRecord.calcLuncaiCount,
		PageLuncaiUsers:               fromAppCollect.pageLuncaiUsers,
		CalcHepanUsers:                fromUserHepanRecord.CalcHepanUsers,
		CalcHepanCount:                fromUserHepanRecord.CalcHepanCount,
		PageHepanUsers:                fromAppCollect.pageHepanUsers,
		CalcPeishiUsers:               fromUserPaipanRecord.calcPeishiUsers,
		CalcPeishiCount:               fromUserPaipanRecord.calcPeishiCount,
		PagePeishiUsers:               fromAppCollect.pagePeishiUsers,
		CalcQingganUsers:              fromUserPaipanRecord.calcQingganUsers,
		CalcQingganCount:              fromUserPaipanRecord.calcQingganCount,
		PageQingganUsers:              fromAppCollect.pageQingganUsers,
		CalcGaokaoUsers:               fromUserPaipanRecord.calcGaokaoUsers,
		CalcGaokaoCount:               fromUserPaipanRecord.calcGaokaoCount,
		PageGaokaoUsers:               fromAppCollect.pageGaokaoUsers,
		PagePayUsers:                  fromAppCollect.pagePayUsers,
		OrderCreateUsers:              fromUserOrder.OrderCreateUsers,
		OrderPayCount:                 fromUserOrder.OrderPayCount,
		OrderPayUsers:                 fromUserOrder.OrderPayUsers,
		OrderPayAmount:                fromUserOrder.OrderPayAmount,
		PaidUserArpu:                  fromUserOrder.OrderPayAmount / fromUserOrder.OrderPayUsers,
		OrderCreateRate:               float64(fromUserOrder.OrderCreateUsers) / float64(fromAppCollect.pagePayUsers),
		OrderPayRate:                  float64(fromUserOrder.OrderPayUsers) / float64(fromUserOrder.OrderCreateUsers),
		NewerActiveRate:               float64(fromAppCollect.newActivatedUsers) / float64(fromAppCollect.activeUsers),
		CalcRate:                      float64(fromCalcUsers.CalcUsers) / float64(fromAppCollect.activeUsers),
		CalcPaipanRate:                float64(fromUserPaipanRecord.calcPaipanUsers) / float64(fromAppCollect.activeUsers),
		CalcYunshiRate:                float64(fromUserPaipanRecord.calcYunshiUsers) / float64(fromAppCollect.activeUsers),
		CalcLuncaiRate:                float64(fromUserPaipanRecord.calcLuncaiUsers) / float64(fromAppCollect.activeUsers),
		CalcHepanRate:                 float64(fromUserHepanRecord.CalcHepanUsers) / float64(fromAppCollect.activeUsers),
		CalcPeishiRate:                float64(fromUserPaipanRecord.calcPeishiUsers) / float64(fromAppCollect.activeUsers),
		CalcGaokaoRate:                float64(fromUserPaipanRecord.calcGaokaoUsers) / float64(fromAppCollect.activeUsers),
	}

	fmt.Println(analysis)

	return nil
}

func setQuery(qry string, date string) string {
	parse, err := time.Parse(time.DateOnly, date)
	if err != nil {
		panic(err)
	}
	next := parse.AddDate(0, 0, 1)
	qry = strings.ReplaceAll(qry, "2025-08-25", date)
	qry = strings.ReplaceAll(qry, "2025-08-26", next.Format("2006-01-02"))
	return qry
}
