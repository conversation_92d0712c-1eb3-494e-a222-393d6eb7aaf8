package main

import (
	"time"

	"github.com/uptrace/bun"
)

type DailyUserAnalysis struct {
	bun.BaseModel                 `bun:"table:daily_user_analysis,alias:dua"`
	ID                            int64     `bun:",pk,autoincrement" json:"id"`                                                        // 主键
	Date                          string    `bun:"date,notnull" json:"date"`                                                           // 日期
	NewActivatedUsers             int       `bun:"new_activated_users,notnull" json:"new_activated_users"`                             // 新增激活用户数（首次打开app，使用oaid）
	NewRegisteredUsers            int       `bun:"new_registered_users,notnull" json:"new_registered_users"`                           // 新增注册用户数（不包括注册后注销）
	NewRegisteredUsersWithDeleted int       `bun:"new_registered_users_with_deleted,notnull" json:"new_registered_users_with_deleted"` // 新增注册用户数（包括注册后注销）
	NewQwContactUsers             int       `bun:"new_qw_contact_users,notnull" json:"new_qw_contact_users"`                           // 企微加粉人数
	ActiveUsers                   int       `bun:"active_users,notnull" json:"active_users"`                                           // 当日活跃用户数
	TotalUsers                    int       `bun:"total_users_to_date,notnull" json:"total_users_to_date"`                             // 当前总注册用户数（不包括注册后注销）
	TotalUsersWithDeleted         int       `bun:"total_users_to_date_with_deleted,notnull" json:"total_users_to_date_with_deleted"`   // 当前总注册用户数（包括注册后注销）
	CalcUsers                     int       `bun:"calc_users,notnull" json:"calc_users"`                                               // 当日测算总用户数=排盘用户数+运势用户数+论财用户数+合盘用户数+配饰用户数+高考用户数+情感用户数
	CalcCount                     int       `bun:"calc_count,notnull" json:"calc_count"`                                               // 当日测算总次数=排盘使用次数+运势使用次数+论财使用次数+合盘使用次数+配饰使用次数+高考使用次数+情感使用次数
	CalcAvgCount                  float64   `bun:"calc_avg_count,notnull" json:"calc_avg_count"`                                       // 当日人均测算次数=当日测算总次数/当日测算总用户数
	CalcPaipanUsers               int       `bun:"calc_paipan_users,notnull" json:"calc_paipan_users"`                                 // 当日排盘使用用户数
	CalcPaipanCount               int       `bun:"calc_paipan_count,notnull" json:"calc_paipan_count"`                                 // 当日排盘使用次数
	CalcYunshiUsers               int       `bun:"calc_yunshi_users,notnull" json:"calc_yunshi_users"`                                 // 当日运势使用用户数
	CalcYunshiCount               int       `bun:"calc_yunshi_count,notnull" json:"calc_yunshi_count"`                                 // 当日运势使用次数
	PageYunshiUsers               int       `bun:"page_yunshi_users,notnull" json:"page_yunshi_users"`                                 // 当日进入运势页面用户数
	CalcLuncaiUsers               int       `bun:"calc_luncai_users,notnull" json:"calc_luncai_users"`                                 // 当日论财使用用户数
	CalcLuncaiCount               int       `bun:"calc_luncai_count,notnull" json:"calc_luncai_count"`                                 // 当日论财使用次数
	PageLuncaiUsers               int       `bun:"page_luncai_users,notnull" json:"page_luncai_users"`                                 // 当日进入论财页面用户数
	CalcHepanUsers                int       `bun:"calc_hepan_users,notnull" json:"calc_hepan_users"`                                   // 当日合盘使用用户数
	CalcHepanCount                int       `bun:"calc_hepan_count,notnull" json:"calc_hepan_count"`                                   // 当日合盘使用次数
	PageHepanUsers                int       `bun:"page_hepan_users,notnull" json:"page_hepan_users"`                                   // 当日进入合盘页面用户数
	CalcPeishiUsers               int       `bun:"calc_peishi_users,notnull" json:"calc_peishi_users"`                                 // 当日配饰使用用户数
	CalcPeishiCount               int       `bun:"calc_peishi_count,notnull" json:"calc_peishi_count"`                                 // 当日配饰使用次数
	PagePeishiUsers               int       `bun:"page_peishi_users,notnull" json:"page_peishi_users"`                                 // 当日进入配饰页面用户数
	CalcQingganUsers              int       `bun:"calc_qinggan_users,notnull" json:"calc_qinggan_users"`                               // 当日情感使用用户数
	CalcQingganCount              int       `bun:"calc_qinggan_count,notnull" json:"calc_qinggan_count"`                               // 当日情感使用次数
	PageQingganUsers              int       `bun:"page_qinggan_users,notnull" json:"page_qinggan_users"`                               // 当日进入情感页面用户数
	CalcGaokaoUsers               int       `bun:"calc_gaokao_users,notnull" json:"calc_gaokao_users"`                                 // 当日高考使用用户数
	CalcGaokaoCount               int       `bun:"calc_gaokao_count,notnull" json:"calc_gaokao_count"`                                 // 当日高考使用次数
	PageGaokaoUsers               int       `bun:"page_gaokao_users,notnull" json:"page_gaokao_users"`                                 // 当日进入高考页面用户数
	PagePayUsers                  int       `bun:"page_pay_users,notnull" json:"page_pay_users"`                                       // 当日进入付费页面用户数
	OrderCreateUsers              int       `bun:"order_create_users,notnull" json:"order_create_users"`                               // 当日创建订单人数
	OrderPayCount                 int       `bun:"order_pay_count,notnull" json:"order_pay_count"`                                     // 当日支付订单数
	OrderPayUsers                 int       `bun:"order_pay_users,notnull" json:"order_pay_users"`                                     // 当日支付成功人数
	OrderPayAmount                int       `bun:"order_pay_amount,notnull" json:"order_pay_amount"`                                   // 当日支付成功金额
	PaidUserArpu                  int       `bun:"paid_user_arpu,notnull" json:"paid_user_arpu"`                                       // arpu=当日支付成功金额/当日支付成功人数
	OrderCreateRate               float64   `bun:"order_create_rate,notnull" json:"order_create_rate"`                                 // 下单率=当日创建订单人数/当日进入付费页面用户数
	OrderPayRate                  float64   `bun:"order_pay_rate,notnull" json:"order_pay_rate"`                                       // 支付率=当日支付成功人数/当日创建订单人数
	NewerActiveRate               float64   `bun:"newer_active_rate,notnull" json:"newer_active_rate"`                                 // 新增活跃占比=当日新增激活用户数/当日活跃用户数
	CalcRate                      float64   `bun:"calc_rate,notnull" json:"calc_rate"`                                                 // 测算率=当日测算用户数/当日活跃用户数
	CalcPaipanRate                float64   `bun:"calc_paipan_rate,notnull" json:"calc_paipan_rate"`                                   // 排盘率=当日排盘使用用户数/当日活跃用户数
	CalcYunshiRate                float64   `bun:"calc_yunshi_rate,notnull" json:"calc_yunshi_rate"`                                   // 运势率=当日运势使用用户数/当日活跃用户数
	CalcLuncaiRate                float64   `bun:"calc_luncai_rate,notnull" json:"calc_luncai_rate"`                                   // 论财率=当日论财使用用户数/当日活跃用户数
	CalcHepanRate                 float64   `bun:"calc_hepan_rate,notnull" json:"calc_hepan_rate"`                                     // 合盘率=当日合盘使用用户数/当日活跃用户数
	CalcPeishiRate                float64   `bun:"calc_peishi_rate,notnull" json:"calc_peishi_rate"`                                   // 配饰率=当日配饰使用用户数/当日活跃用户数
	CalcGaokaoRate                float64   `bun:"calc_gaokao_rate,notnull" json:"calc_gaokao_rate"`                                   // 高考率=当日高考使用用户数/当日活跃用户数
	RetentionD1Users              int       `bun:"retention_d1_users,notnull" json:"retention_d1_users"`                               // 次日留存用户数
	RetentionD3Users              int       `bun:"retention_d3_users,notnull" json:"retention_d3_users"`                               // 3日留存用户数
	RetentionD7Users              int       `bun:"retention_d7_users,notnull" json:"retention_d7_users"`                               // 7日留存用户数
	RetentionD14Users             int       `bun:"retention_d14_users,notnull" json:"retention_d14_users"`                             // 14日留存用户数
	RetentionWeek2Users           int       `bun:"retention_week2_users,notnull" json:"retention_week2_users"`                         // 次周留存用户数
	RetentionMonth2Users          int       `bun:"retention_month2_users,notnull" json:"retention_month2_users"`                       // 次月留存用户数
	CreatedAt                     time.Time `bun:"created_at,notnull" json:"created_at"`
	UpdatedAt                     time.Time `bun:"updated_at,notnull" json:"updated_at"`
}

type (
	FromAppCollect struct {
		NewActivatedUsers int `bun:"new_activated_users"`
		ActiveUsers       int `bun:"active_users"`
		PageYunshiUsers   int `bun:"page_yunshi_users"`
		PageLuncaiUsers   int `bun:"page_luncai_users"`
		PageHepanUsers    int `bun:"page_hepan_users"`
		PagePeishiUsers   int `bun:"page_peishi_users"`
		PageGaokaoUsers   int `bun:"page_gaokao_users"`
		PageQingganUsers  int `bun:"page_qinggan_users"`
		PagePayUsers      int `bun:"page_pay_users"`
	}
	FromAppUser struct {
		NewRegisteredUsers            int `bun:"new_registered_users"`
		NewRegisteredUsersWithDeleted int `bun:"new_registered_users_with_deleted"`
		TotalUsers                    int `bun:"total_users_to_date"`
		TotalUsersWithDeleted         int `bun:"total_users_to_date_with_deleted"`
	}
	FromUserPaipanRecord struct {
		calcUsers        int `bun:"calc_users"`
		calcPaipanUsers  int `bun:"calc_paipan_users"`
		calcPaipanCount  int `bun:"calc_paipan_count"`
		calcYunshiUsers  int `bun:"calc_yunshi_users"`
		calcYunshiCount  int `bun:"calc_yunshi_count"`
		calcLuncaiUsers  int `bun:"calc_luncai_users"`
		calcLuncaiCount  int `bun:"calc_luncai_count"`
		calcHepanUsers   int `bun:"calc_hepan_users"`
		calcHepanCount   int `bun:"calc_hepan_count"`
		calcPeishiUsers  int `bun:"calc_peishi_users"`
		calcPeishiCount  int `bun:"calc_peishi_count"`
		calcGaokaoUsers  int `bun:"calc_gaokao_users"`
		calcGaokaoCount  int `bun:"calc_gaokao_count"`
		calcQingganUsers int `bun:"calc_qinggan_users"`
		calcQingganCount int `bun:"calc_qinggan_count"`
	}

	FromCalcUsers struct {
		CalcUsers int `bun:"calc_users"`
	}

	FromUserHepanRecord struct {
		CalcHepanUsers int `bun:"calc_hepan_users"`
		CalcHepanCount int `bun:"calc_hepan_count"`
	}

	FromUserOrder struct {
		OrderCreateUsers int `bun:"order_create_users"`
		OrderPayCount    int `bun:"order_pay_count"`
		OrderPayUsers    int `bun:"order_pay_users"`
		OrderPayAmount   int `bun:"order_pay_amount"`
	}

	FromQwContactFollow struct {
		NewQwContactUsers int `bun:"new_qw_contact_users"`
	}
)
