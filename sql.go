package main

var (
	// 从app_collect表中获取数据
	QueryFromAppCollect = `
SELECT
    -- 新增激活用户
    COUNT(DISTINCT CASE WHEN ac.event = 'app_launch'
        AND ac.event_time >= '2025-08-25' AND ac.event_time < '2025-08-26'
        AND ac.event_param->>'$.is_first_launch' = 'true'
                            THEN ac.oaid END) AS new_activated_users,

    -- 当日活跃用户
    COUNT(DISTINCT CASE WHEN ac.event = 'app_launch'
        AND ac.event_time >= '2025-08-25' AND ac.event_time < '2025-08-26'
                            THEN ac.oaid END) AS active_users,

    -- 运势页面人数
    COUNT(DISTINCT CASE WHEN ac.event = 'home_app_click'
        AND ac.event_param->>'$.app_name' = '2025运势'
        AND ac.event_time BETWEEN '2025-08-25' AND '2025-08-26'
          THEN ac.uid END) AS page_yunshi_users,

    -- 论财页面人数
    COUNT(DISTINCT CASE WHEN ac.event = 'home_app_click'
        AND ac.event_param->>'$.app_name' = '论玄论财'
        AND ac.event_time BETWEEN '2025-08-25' AND '2025-08-26'
          THEN ac.uid END) AS page_luncai_users,

    -- 合盘页面人数
    COUNT(DISTINCT CASE WHEN ac.event = 'home_app_click'
        AND ac.event_param->>'$.app_name' = '八字合盘'
        AND ac.event_time BETWEEN '2025-08-25' AND '2025-08-26'
          THEN ac.uid END) AS page_hepan_users,

    -- 配饰页面人数（两种入口合并）
    COUNT(DISTINCT CASE WHEN ac.event = 'home_app_click'
        AND ac.event_param->>'$.app_name' = '增运配饰'
        AND ac.event_time BETWEEN '2025-08-25' AND '2025-08-26'
          THEN ac.uid END)
        + COUNT(DISTINCT CASE WHEN ac.event = 'click_home_carousel'
        AND ac.event_param->>'$.banner_id' = '7'
        AND ac.event_time BETWEEN '2025-08-25' AND '2025-08-26'
                THEN ac.uid END) AS page_peishi_users,

    -- 高考页面人数
    COUNT(DISTINCT CASE WHEN ac.event = 'click_home_carousel'
        AND ac.event_param->>'$.banner_id' = '3'
        AND ac.event_time BETWEEN '2025-08-25' AND '2025-08-26'
          THEN ac.uid END) AS page_gaokao_users,

    -- 情感页面人数
    COUNT(DISTINCT CASE WHEN ac.event = 'home_app_click'
        AND ac.event_param->>'$.app_name' = '流年情感'
        AND ac.event_time BETWEEN '2025-08-25' AND '2025-08-26'
          THEN ac.uid END) AS page_qinggan_users,

    -- 进入付费页人数
    COUNT(DISTINCT CASE WHEN ac.event = 'to_member'
        AND ac.event_time BETWEEN '2025-08-25' AND '2025-08-26'
                            THEN ac.uid END) AS page_pay_users

FROM app_collect ac;
`

	// 从app_user表中获取数据
	QueryFromAppUser = `
SELECT
    COUNT(CASE WHEN au.created_at BETWEEN '2025-08-25' AND '2025-08-26'
        AND au.deleted_at = '0001-01-01 00:00:00' THEN au.id END) AS new_registered_users,
    COUNT(CASE WHEN au.created_at BETWEEN '2025-08-25' AND '2025-08-26'
                   THEN au.id END) AS new_registered_users_with_deleted,
    COUNT(CASE WHEN au.created_at < '2025-08-26'
        AND au.deleted_at = '0001-01-01 00:00:00' THEN au.id END) AS total_users_to_date,
    COUNT(CASE WHEN au.created_at < '2025-08-26' THEN au.id END) AS total_users_to_date_with_deleted
FROM app_user au;
`

	// 从user_paipan_record表中获取数据
	QueryFromUserPaipanRecord = `
SELECT
    COUNT(DISTINCT CASE WHEN upr.app_id = 2
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.user_id END) AS calc_paipan_users,
    COUNT(CASE WHEN upr.app_id = 2
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.id END) AS calc_paipan_count,

    COUNT(DISTINCT CASE WHEN upr.app_id = 4
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.user_id END) AS calc_yunshi_users,
    COUNT(CASE WHEN upr.app_id = 4
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.id END) AS calc_yunshi_count,

    COUNT(DISTINCT CASE WHEN upr.app_id = 5
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.user_id END) AS calc_luncai_users,
    COUNT(CASE WHEN upr.app_id = 5
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.id END) AS calc_luncai_count,

    COUNT(DISTINCT CASE WHEN upr.app_id = 9
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.user_id END) AS calc_peishi_users,
    COUNT(CASE WHEN upr.app_id = 9
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.id END) AS calc_peishi_count,

    COUNT(DISTINCT CASE WHEN upr.app_id = 7
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.user_id END) AS calc_gaokao_users,
    COUNT(CASE WHEN upr.app_id = 7
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.id END) AS calc_gaokao_count,
    
    COUNT(DISTINCT CASE WHEN upr.app_id = 10
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.user_id END) AS calc_qinggan_users,
    COUNT(CASE WHEN upr.app_id = 10
        AND upr.created_at BETWEEN '2025-08-25' AND '2025-08-26' THEN upr.id END) AS calc_qinggan_count
    
FROM user_paipan_record upr;
`

	// 总排盘人数
	QueryFromCalcUsers = `
SELECT COUNT(DISTINCT user_id) AS calc_users
FROM (
    SELECT user_id
    FROM user_paipan_record
    WHERE app_id IN (2,4,5,9,7,10)
      AND created_at BETWEEN '2025-08-25' AND '2025-08-26'
    
    UNION   -- 去重合并
    
    SELECT user_id
    FROM user_hepan_record
    WHERE created_at BETWEEN '2025-08-25' AND '2025-08-26'
) t;
`

	// 从user_hepan_record表中获取数据
	QueryFromUserHepanRecord = `
SELECT
    COUNT(DISTINCT uhr.user_id) AS calc_hepan_users,
    COUNT(uhr.id) AS calc_hepan_count
FROM user_hepan_record uhr
WHERE uhr.created_at BETWEEN '2025-08-25' AND '2025-08-26';
`

	// 从order表中获取数据
	QueryFromUserOrder = `
SELECT
    COUNT(DISTINCT o.user_id) AS order_create_users,
    COUNT(CASE WHEN o.pay_status = 1 THEN o.id END) AS order_pay_count,
    COUNT(DISTINCT CASE WHEN o.pay_status = 1 THEN o.user_id END) AS order_pay_users,
    COALESCE(SUM(CASE WHEN o.pay_status = 1 THEN o.pay_amount END), 0) AS order_pay_amount
FROM ` + "`order`" + ` o
WHERE o.created_at BETWEEN '2025-08-25' AND '2025-08-26';
`

	// 从qw_contact_follow表中获取数据
	QueryFromQwContactFollow = `
SELECT COUNT(DISTINCT user_id) AS new_qw_contact_users
FROM qw_contact_follow
WHERE created_at BETWEEN '2025-08-25' AND '2025-08-26';
`
)
