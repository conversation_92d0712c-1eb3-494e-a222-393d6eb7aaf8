CREATE TABLE IF NOT EXISTS `daily_report` (
                                              `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `report_date` date NOT NULL COMMENT '报告日期',

    -- 用户指标
    `new_activated_users` int(11) DEFAULT 0 COMMENT '新增激活用户数',
    `new_registered_users` int(11) DEFAULT 0 COMMENT '新增注册用户数（不含删除）',
    `new_registered_users_with_deleted` int(11) DEFAULT 0 COMMENT '新增注册用户数（含删除）',
    `new_qw_contact_users` int(11) DEFAULT 0 COMMENT '企业微信加粉人数',
    `active_users` int(11) DEFAULT 0 COMMENT '当日活跃用户数',
    `total_activated_users` int(11) DEFAULT 0 COMMENT '总激活用户数',
    `total_users` int(11) DEFAULT 0 COMMENT '总注册用户数（不含删除）',
    `total_users_with_deleted` int(11) DEFAULT 0 COMMENT '总注册用户数（含删除）',

    -- 功能使用指标
    `calc_paipan_users` int(11) DEFAULT 0 COMMENT '排盘测算用户数',
    `calc_paipan_count` int(11) DEFAULT 0 COMMENT '排盘测算次数',
    `calc_yunshi_users` int(11) DEFAULT 0 COMMENT '运势测算用户数',
    `calc_yunshi_count` int(11) DEFAULT 0 COMMENT '运势测算次数',
    `page_yunshi_users` int(11) DEFAULT 0 COMMENT '运势页面进入人数',
    `calc_luncai_users` int(11) DEFAULT 0 COMMENT '论财测算用户数',
    `calc_luncai_count` int(11) DEFAULT 0 COMMENT '论财测算次数',
    `page_luncai_users` int(11) DEFAULT 0 COMMENT '论财页面进入人数',
    `calc_hepan_users` int(11) DEFAULT 0 COMMENT '合盘测算用户数',
    `calc_hepan_count` int(11) DEFAULT 0 COMMENT '合盘测算次数',
    `page_hepan_users` int(11) DEFAULT 0 COMMENT '合盘页面进入人数',
    `calc_peishi_users` int(11) DEFAULT 0 COMMENT '配饰测算用户数',
    `calc_peishi_count` int(11) DEFAULT 0 COMMENT '配饰测算次数',
    `page_peishi_users` int(11) DEFAULT 0 COMMENT '配饰页面进入人数',
    `calc_gaokao_users` int(11) DEFAULT 0 COMMENT '高考测算用户数',
    `calc_gaokao_count` int(11) DEFAULT 0 COMMENT '高考测算次数',
    `page_gaokao_users` int(11) DEFAULT 0 COMMENT '高考页面进入人数',
    `calc_qinggan_users` int(11) DEFAULT 0 COMMENT '流年情感测算用户数',
    `calc_qinggan_count` int(11) DEFAULT 0 COMMENT '流年情感测算次数',
    `page_qinggan_users` int(11) DEFAULT 0 COMMENT '流年情感页面进入人数',

    -- 付费相关指标
    `to_member_page_users` int(11) DEFAULT 0 COMMENT '进入付费页面人数',
    `order_create_users` int(11) DEFAULT 0 COMMENT '创建订单人数',
    `paid_order_count` int(11) DEFAULT 0 COMMENT '支付订单数',
    `paid_order_users` int(11) DEFAULT 0 COMMENT '支付订单人数',
    `paid_amount` int(11) DEFAULT 0 COMMENT '支付金额',

    -- 计算字段（占比、率等）
    `total_calc_users` int(11) DEFAULT 0 COMMENT '所有功能总测算人数',
    `total_calc_count` int(11) DEFAULT 0 COMMENT '所有功能总测算次数',
    `avg_calc_per_user` decimal(5,2) DEFAULT 0.00 COMMENT '人均测算次数',
    `paid_user_arpu` int(11) DEFAULT 0 COMMENT '付费用户ARPU',
    `order_create_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '订单创建率',
    `payment_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '付费率',
    `new_active_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '新增活跃占比',
    `calc_active_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '总测算占比',

    -- 各功能测算占比
    `paipan_calc_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '排盘测算占比',
    `yunshi_calc_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '运势测算占比',
    `luncai_calc_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '论财测算占比',
    `hepan_calc_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '合盘测算占比',
    `peishi_calc_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '配饰测算占比',
    `gaokao_calc_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '高考测算占比',
    `qinggan_calc_ratio` decimal(5,4) DEFAULT 0.0000 COMMENT '流年情感测算占比',

    -- 留存相关指标
    `retention_d1_users` int(11) DEFAULT 0 COMMENT '次日留存用户数',
    `retention_d3_users` int(11) DEFAULT 0 COMMENT '3日留存用户数',
    `retention_d7_users` int(11) DEFAULT 0 COMMENT '7日留存用户数',
    `retention_d14_users` int(11) DEFAULT 0 COMMENT '14日留存用户数',
    `retention_week2_users` int(11) DEFAULT 0 COMMENT '次周留存用户数（第8~14天）',
    `retention_month2_users` int(11) DEFAULT 0 COMMENT '次月留存用户数（第31~60天）',
    `retention_d1_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '次日留存率',
    `retention_d3_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '3日留存率',
    `retention_d7_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '7日留存率',
    `retention_d14_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '14日留存率',
    `retention_week2_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '次周留存率',
    `retention_month2_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '次月留存率',

    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_report_date` (`report_date`),
    KEY `idx_report_date` (`report_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日报数据表';